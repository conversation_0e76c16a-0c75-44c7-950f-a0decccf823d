using System;
using System.Collections.Generic;
using System.Linq;
using System.Reactive;
using System.Reactive.Subjects;
using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Fusion;
using Fusion.Sockets;
using Modules.Analytics;
using Modules.Core;
using UnityEngine;
using VContainer;
using Log = Modules.Network.Logger.Network;

namespace Modules.Network
{
    internal class NetworkClient : Actor, INetworkClient, INetworkClientConnection
    {
        [SerializeField] private NetworkRunnerProvider provider;
        [SerializeField] private DebugConfig debugConfig;

        private float sessionConnectionTime;
        private IAnalyticsClient analyticsClient;
        private MasterClientHandler masterClientHandler;
        private CancellationTokenSource disconnectionCancellationTokenSource;
        private readonly IAsyncReactiveProperty<PlayerRef> masterClient = new AsyncReactiveProperty<PlayerRef>(PlayerRef.None);
        private readonly IAsyncReactiveProperty<bool> isMasterClient = new AsyncReactiveProperty<bool>(false);
        private readonly IAsyncReactiveProperty<int> playerCount = new AsyncReactiveProperty<int>(0);
        private readonly IAsyncReactiveProperty<bool> isConnected = new AsyncReactiveProperty<bool>(false);
        private readonly ISubject<(NetworkRunner runner, PlayerRef player)> onPlayerJoined = new Subject<(NetworkRunner runner, PlayerRef player)>();
        private readonly ISubject<(NetworkRunner runner, PlayerRef player)> onPlayerLeft = new Subject<(NetworkRunner runner, PlayerRef player)>();
        private readonly ISubject<(NetworkRunner runner, ShutdownReason reason)> onShutdown = new Subject<(NetworkRunner runner, ShutdownReason shutdownReason)>();
        private readonly ISubject<NetworkRunner> onConnectedToServer = new Subject<NetworkRunner>();
        private readonly ISubject<(NetworkRunner runner, NetDisconnectReason reason)> onDisconnectedFromServer = new Subject<(NetworkRunner, NetDisconnectReason)>();
        private readonly ISubject<(NetworkRunner runner, List<SessionInfo> sessionList)> onSessionListUpdated = new Subject<(NetworkRunner runner, List<SessionInfo> sessionList)>();

        private readonly ISubject<(NetworkRunner runner, PlayerRef player, ReliableKey key, ArraySegment<byte> data)> onReliableDataReceived =
            new Subject<(NetworkRunner runner, PlayerRef player, ReliableKey key, ArraySegment<byte> data)>();

        private readonly ISubject<(NetworkRunner runner, PlayerRef player, ReliableKey key, float progress)> onReliableDataProgress =
            new Subject<(NetworkRunner runner, PlayerRef player, ReliableKey key, float progress)>();

        private readonly ISubject<(NetworkRunner runner, NetworkActor actor)> onNetworkActorSpawned = new Subject<(NetworkRunner runner, NetworkActor actor)>();
        private readonly ISubject<(NetworkRunner runner, NetworkActor actor)> onNetworkActorDespawned = new Subject<(NetworkRunner runner, NetworkActor actor)>();
        private readonly ISubject<NetworkRunner> onReadyMasterObjectSpawn = new Subject<NetworkRunner>();

        public PlayerRef LocalPlayer => HasRunner ? Runner.LocalPlayer : default;
        public float LocalTime => sessionConnectionTime == 0 ? 0 : Time.time - sessionConnectionTime;
        public float ServerTime => HasRunner ? Runner.RemoteRenderTime : 0;
        public int LatestServerTick => HasRunner ? Runner.LatestServerTick : 0;
        public IEnumerable<PlayerRef> ActivePlayers => HasRunner ? Runner.ActivePlayers : Array.Empty<PlayerRef>();
        public IReadOnlyAsyncReactiveProperty<PlayerRef> MasterClient => masterClient;
        public IReadOnlyAsyncReactiveProperty<bool> IsMasterClient => isMasterClient;
        public IReadOnlyAsyncReactiveProperty<int> PlayerCount => playerCount;
        public IReadOnlyAsyncReactiveProperty<bool> IsConnected => isConnected;

        public CancellationToken DisconnectionCancellationToken
        {
            get
            {
                disconnectionCancellationTokenSource ??= new CancellationTokenSource();
                return disconnectionCancellationTokenSource.Token;
            }
        }

        public LobbyInfo LobbyInfo => HasRunner ? Runner.LobbyInfo : null;
        public SessionInfo SessionInfo => HasRunner ? Runner.SessionInfo : null;
        public IObservable<(NetworkRunner runner, PlayerRef player)> OnPlayerJoined => onPlayerJoined;
        public IObservable<(NetworkRunner runner, PlayerRef player)> OnPlayerLeft => onPlayerLeft;
        public IObservable<(NetworkRunner runner, ShutdownReason reason)> OnShutdown => onShutdown;
        public IObservable<NetworkRunner> OnConnectedToServer => onConnectedToServer;
        public IObservable<(NetworkRunner runner, NetDisconnectReason reason)> OnDisconnectedFromServer => onDisconnectedFromServer;
        public IObservable<(NetworkRunner runner, List<SessionInfo> sessionList)> OnSessionListUpdated => onSessionListUpdated;
        public IObservable<(NetworkRunner runner, PlayerRef player, ReliableKey key, ArraySegment<byte> data)> OnReliableDataReceived => onReliableDataReceived;
        public IObservable<(NetworkRunner runner, PlayerRef player, ReliableKey key, float progress)> OnReliableDataProgress => onReliableDataProgress;
        public IObservable<(NetworkRunner runner, NetworkActor actor)> OnNetworkActorSpawned => onNetworkActorSpawned;
        public IObservable<(NetworkRunner runner, NetworkActor actor)> OnNetworkActorDespawned => onNetworkActorDespawned;
        public IObservable<Unit> OnRunnerDestroyed => provider.OnRunnerDestroyed;
        public IObservable<NetworkRunner> OnReadyMasterObjectSpawn => onReadyMasterObjectSpawn;

        private NetworkRunner Runner => provider.Runner;
        private INetworkSceneManager SceneManager => provider.SceneManager;
        private bool HasRunner => provider.HasRunner;

        [Inject]
        private void Construct(IAnalyticsClient analyticsClient)
        {
            this.analyticsClient = analyticsClient;
        }

        private void Awake()
        {
            provider.OnRunnerCreated.Subscribe(HandleRunnerCreated).AddTo(destroyCancellationToken);
            provider.OnRunnerDestroyed.Subscribe(HandleRunnerDestroyed).AddTo(destroyCancellationToken);
            NetworkActorDispatcher.OnSpawned.Subscribe(HandleActorSpawned).AddTo(destroyCancellationToken);
            NetworkActorDispatcher.OnDespawned.Subscribe(HandleActorDespawned).AddTo(destroyCancellationToken);
        }

        private void OnDestroy()
        {
            CancelDisconnectionToken();
        }

        public async UniTask<StartGameResult> ConnectSession(ConnectSessionArgs connectSessionArgs, CancellationToken cancellationToken = default)
        {
            CancelDisconnectionToken();
            provider.CreateIfNull();

            var args = new StartGameArgs
            {
                GameMode = debugConfig.UseNetworkMock ? GameMode.Single : GameMode.Shared,
                SessionProperties = connectSessionArgs.properties,
                PlayerCount = connectSessionArgs.playerCount,
                SceneManager = SceneManager,
                AuthValues = connectSessionArgs.authentication,
                MatchmakingMode = connectSessionArgs.matchmakingMode,
                StartGameCancellationToken = cancellationToken
            };

            Log.Debug("Connecting to session: {0}", args);

            var result = await ConnectSessionInternal(args, cancellationToken);

            if (result.Ok)
            {
#if UNITY_EDITOR
                if (args.GameMode == GameMode.Single)
                {
                    await UniTask.Delay(TimeSpan.FromSeconds(1), cancellationToken: cancellationToken);
                }
#endif
                sessionConnectionTime = Time.time;
                Log.Debug("Connected to session {0}", SessionInfo);
            }
            else
            {
                analyticsClient.Track("connect_session_error", new Dictionary<string, object> { { "reason", result.ShutdownReason.ToString() } });
                Log.Fatal(new NetworkException(result.ToString()));
            }

            return result;
        }

        private async UniTask<StartGameResult> ConnectSessionInternal(StartGameArgs args, CancellationToken cancellationToken)
        {
            var result = await Runner.StartGame(args).AsUniTask().AttachExternalCancellation(cancellationToken);
            if (!result.Ok && result.ShutdownReason == ShutdownReason.GameIsFull)
            {
                analyticsClient.Track("game_full_error", new Dictionary<string, object> { { "state", "happened" } });

                args.SessionName = Guid.NewGuid().ToString();
                result = await Runner.StartGame(args).AsUniTask().AttachExternalCancellation(cancellationToken);

                var stateValue = result.Ok ? "resolved" : "failed";
                analyticsClient.Track("game_full_error", new Dictionary<string, object> { { "state", stateValue } });
            }

            return result;
        }

        public async UniTask Disconnect(ShutdownReason reason = ShutdownReason.Ok, CancellationToken cancellationToken = default)
        {
            if (!HasRunner)
            {
                return;
            }

            Log.Debug("Disconnecting");

            await Runner.Shutdown(true, reason).AsUniTask().AttachExternalCancellation(cancellationToken);
            await UniTask.WaitWhile(() => HasRunner, cancellationToken: cancellationToken).TimeoutWithoutException(TimeSpan.FromSeconds(5));
            provider.DestroyIfNotNull();
            await UniTask.Delay(TimeSpan.FromSeconds(1), cancellationToken: cancellationToken);

            Log.Debug("Disconnected");
        }

        public void Spawn(NetworkObject prefab, Vector3? position = null, Quaternion? rotation = null, NetworkRunner.OnBeforeSpawned onBeforeSpawned = null)
        {
            if (!HasRunner)
            {
                return;
            }

            Runner.Spawn(prefab, position, rotation, LocalPlayer, onBeforeSpawned);
        }

        public void Despawn(NetworkObject networkObject)
        {
            if (!HasRunner)
            {
                return;
            }

            Runner.Despawn(networkObject);
        }

        public void SendReliableDataToPlayer(PlayerRef playerRef, ReliableKey reliableKey, byte[] data)
        {
            if (!HasRunner)
            {
                return;
            }

            Runner.SendReliableDataToPlayer(playerRef, reliableKey, data);
        }

        public bool TryGetNetworkActorList<T>(out List<T> result) where T : NetworkActor
        {
            result = new List<T>();

            if (!HasRunner)
            {
                return false;
            }

            foreach (var networkObject in Runner.GetAllNetworkObjects())
            {
                if (networkObject.TryGetComponent<T>(out var actor))
                {
                    result.Add(actor);
                }
            }

            return result.Count > 0;
        }

        public bool HasNetworkActor<T>() where T : NetworkActor
        {
            return HasRunner && Runner.GetAllNetworkObjects().Exists(networkObject => networkObject.TryGetComponent<T>(out _));
        }

        public bool HasPlayer(PlayerRef playerRef)
        {
            return HasPlayer(playerRef.PlayerId);
        }

        public bool HasPlayer(int playerId)
        {
            foreach (var activePlayer in ActivePlayers)
            {
                if (activePlayer.PlayerId == playerId)
                {
                    return true;
                }
            }

            return false;
        }

        public void SetConnection(bool isConnected)
        {
            if (this.isConnected.Value == isConnected)
            {
                return;
            }

            Log.Debug("Connection status: {0}", isConnected ? "Connected" : "Disconnected");

            this.isConnected.Value = isConnected;
        }

        private void ResetMasterClient()
        {
            if (isMasterClient.Value)
            {
                isMasterClient.Value = false;
            }

            if (masterClient.Value != PlayerRef.None)
            {
                masterClient.Value = PlayerRef.None;
            }
        }

        private void UpdatePlayerCount()
        {
            if (!HasRunner || playerCount.Value == Runner.ActivePlayers.Count())
            {
                return;
            }

            playerCount.Value = Runner.ActivePlayers.Count();
        }

        private void ResetPlayerCount()
        {
            if (playerCount.Value == 0)
            {
                return;
            }

            playerCount.Value = 0;
        }

        private void HandleRunnerCreated(NetworkRunnerActor actor)
        {
            var callbacks = actor.RunnerCallbacks;
            var token = callbacks.destroyCancellationToken;

            callbacks.OnPlayerJoined.Subscribe(HandlePlayerJoined).AddTo(token);
            callbacks.OnPlayerLeft.Subscribe(HandlePlayerLeft).AddTo(token);
            callbacks.OnShutdown.Subscribe(HandleShutdown).AddTo(token);
            callbacks.OnConnectedToServer.Subscribe(HandleConnectedToServer).AddTo(token);
            callbacks.OnDisconnectedFromServer.Subscribe(HandleDisconnectedFromServer).AddTo(token);
            callbacks.OnConnectFailed.Subscribe(HandleConnectFailed).AddTo(token);
            callbacks.OnSessionListUpdated.Subscribe(onSessionListUpdated.OnNext).AddTo(token);
            callbacks.OnReliableDataReceived.Subscribe(onReliableDataReceived.OnNext).AddTo(token);
            callbacks.OnReliableDataProgress.Subscribe(onReliableDataProgress.OnNext).AddTo(token);
        }

        private void HandleRunnerDestroyed(Unit unit)
        {
            SetConnection(false);
            CancelDisconnectionToken();
            ResetPlayerCount();
            ResetMasterClient();
            sessionConnectionTime = 0;
        }

        private void HandlePlayerJoined((NetworkRunner runner, PlayerRef player) tuple)
        {
            UpdatePlayerCount();
            onPlayerJoined.OnNext(tuple);

            if (tuple.runner.IsSharedModeMasterClient && tuple.player == LocalPlayer && tuple.player.PlayerId <= 1)
            {
                onReadyMasterObjectSpawn.OnNext(Runner);
            }
        }

        private void HandlePlayerLeft((NetworkRunner runner, PlayerRef player) tuple)
        {
            UpdatePlayerCount();
            onPlayerLeft.OnNext(tuple);
        }

        private void HandleShutdown((NetworkRunner runner, ShutdownReason reason) tuple)
        {
            if (tuple.reason != ShutdownReason.Ok)
            {
                var reasonMessage = tuple.reason.ToString();
                Log.Error(reasonMessage);

                if (tuple.reason != ShutdownReason.DisconnectedByPluginLogic)
                {
                    analyticsClient.Track("shutdown_error", new Dictionary<string, object> { { "reason", reasonMessage } });
                }
            }

            SetConnection(false);
            CancelDisconnectionToken();
            ResetPlayerCount();
            ResetMasterClient();
            onShutdown.OnNext(tuple);
            sessionConnectionTime = 0;
        }

        private void HandleConnectedToServer(NetworkRunner runner)
        {
            onConnectedToServer.OnNext(runner);
        }

        private void HandleDisconnectedFromServer((NetworkRunner runner, NetDisconnectReason reason) tuple)
        {
            var reasonMessage = tuple.reason.ToString();
            Log.Error(reasonMessage);

            onDisconnectedFromServer.OnNext(tuple);
        }

        private void HandleConnectFailed((NetworkRunner runner, NetConnectFailedReason reason) tuple)
        {
            var reasonMessage = tuple.reason.ToString();
            Log.Error(reasonMessage);
        }

        private void HandleActorSpawned(NetworkActor networkActor)
        {
            if (!HasRunner)
            {
                return;
            }

            onNetworkActorSpawned.OnNext((Runner, networkActor));

            if (networkActor is MasterClientHandler masterClientHandler)
            {
                masterClientHandler.MasterClient.Subscribe(HandleMasterClientChanged).AddTo(masterClientHandler);
            }
        }

        private void HandleMasterClientChanged(PlayerRef masterClientValue)
        {
            masterClient.Value = masterClientValue;
            isMasterClient.Value = masterClientValue == LocalPlayer;
        }

        private void HandleActorDespawned(NetworkActor networkActor)
        {
            if (!HasRunner)
            {
                return;
            }

            onNetworkActorDespawned.OnNext((Runner, networkActor));
        }

        private void CancelDisconnectionToken()
        {
            disconnectionCancellationTokenSource.CancelAndDispose();
            disconnectionCancellationTokenSource = null;
        }
    }
}