using UnityEngine.XR.Interaction.Toolkit;
using UnityEngine.XR.Interaction.Toolkit.Filtering;

namespace Modules.XR
{
    /// <summary>
    /// Base class for Interaction Toolkit Filter implementation. Implements all interfaces
    /// with virtual methods to easily override only what's needed.
    /// </summary>
    public abstract class BaseXRFilter : IXRHoverFilter, IXRSelectFilter
    {
        protected virtual bool CanHover => true;
        protected virtual bool CanSelect => true;

        protected virtual bool ProcessHover(IXRHoverInteractor interactor, IXRHoverInteractable interactable) => true;

        protected virtual bool ProcessSelect(IXRSelectInteractor interactor, IXRSelectInteractable interactable) => true;

        bool IXRHoverFilter.canProcess => CanHover;
        bool IXRSelectFilter.canProcess => CanSelect;

        bool IXRHoverFilter.Process(IXRHoverInteractor interactor, IXRHoverInteractable interactable)
        {
            return ProcessHover(interactor, interactable);
        }

        bool IXRSelectFilter.Process(IXRSelectInteractor interactor, IXRSelectInteractable interactable)
        {
            return ProcessSelect(interactor, interactable);
        }
    }
}