using System.Collections.Generic;
using UnityEngine.XR.Interaction.Toolkit;

namespace Modules.XR
{
    public class XRDirectInteractorNearest : XRDirectInteractor
    {
        public override void GetValidTargets(List<IXRInteractable> targets)
        {
            base.GetValidTargets(targets);

            if (targets.Count > 1)
            {
                var target = targets[0];
                targets.Clear();
                targets.Add(target);
            }
        }
    }
}