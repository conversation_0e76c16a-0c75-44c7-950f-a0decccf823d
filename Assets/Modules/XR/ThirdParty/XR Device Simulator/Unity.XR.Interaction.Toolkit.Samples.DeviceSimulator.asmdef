{"name": "Unity.XR.Interaction.Toolkit.Samples.DeviceSimulator", "rootNamespace": "", "references": ["GUID:75469ad4d38634e559750d17036d5f7c", "GUID:dc960734dc080426fa6612f1c5fe95f3", "GUID:fe685ec1767f73d42b749ea8045bfe43"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.xr.hands", "expression": "1.1.0", "define": "XR_HANDS_1_1_OR_NEWER"}], "noEngineReferences": false}