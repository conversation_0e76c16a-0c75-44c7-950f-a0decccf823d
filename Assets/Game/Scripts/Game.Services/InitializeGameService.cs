using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using JetBrains.Annotations;
using Modules.Core;
using Modules.Firebase;
using Modules.Oculus;
using Modules.UnityGameServices;

namespace Game.Services
{
    internal class InitializeGameService : IInitializeGameService
    {
        private static readonly TimeSpan RetryInterval = TimeSpan.FromSeconds(5);

        private readonly AppConfig appConfig;
        private readonly IOculusClient oculusClient;
        private readonly IFirebaseService firebaseService;
        private readonly IAnalyticsService analyticsService;
        private readonly IUnityGameServices unityGameServices;
        private readonly IRemoteConfigService remoteConfigService;
        private readonly IFirebaseTokenService firebaseTokenService;

        public InitializeGameService(
            IOculusClient oculusClient,
            IUnityGameServices unityGameServices,
            IFirebaseTokenService firebaseTokenService,
            IAnalyticsService analyticsService,
            IRemoteConfigService remoteConfigService,
            IFirebaseService firebaseService,
            AppConfig appConfig)
        {
            this.appConfig = appConfig;
            this.oculusClient = oculusClient;
            this.firebaseService = firebaseService;
            this.analyticsService = analyticsService;
            this.unityGameServices = unityGameServices;
            this.remoteConfigService = remoteConfigService;
            this.firebaseTokenService = firebaseTokenService;
        }

        public async UniTask Initialize(CancellationToken cancellationToken)
        {
            await InitializeOculus(cancellationToken);
            await InitializeUnityGameServices(cancellationToken);
            await Login(cancellationToken);
            await InitializeFirebaseService(cancellationToken);
            InitializeAnalytics();
            await SyncRemoteConfig(cancellationToken);
            await SyncEconomyConfiguration(cancellationToken);
            await SyncUserNameIfNotValid(cancellationToken);
            await RefreshFirebaseToken(cancellationToken);
        }

        private async UniTask InitializeOculus(CancellationToken cancellationToken)
        {
            Result result;

            do
            {
                result = await oculusClient.Initialize(cancellationToken);

                if (result.IsFail)
                {
                    await UniTask.Delay(RetryInterval, cancellationToken: cancellationToken);
                }
            } while (result.IsFail);
        }

        private async UniTask InitializeUnityGameServices(CancellationToken cancellationToken)
        {
            Result result;

            do
            {
                result = await unityGameServices.Initialize(appConfig.UnityApiName, cancellationToken);

                if (result.IsFail)
                {
                    await UniTask.Delay(RetryInterval, cancellationToken: cancellationToken);
                }
            } while (result.IsFail);
        }

        private async UniTask InitializeFirebaseService(CancellationToken cancellationToken)
        {
            Result result;

            do
            {
                result = await firebaseService.Initialize(cancellationToken);

                if (result.IsFail)
                {
                    await UniTask.Delay(RetryInterval, cancellationToken: cancellationToken);
                }
            } while (result.IsFail);
        }

        private async UniTask Login(CancellationToken cancellationToken)
        {
            Result result;

            do
            {
                result = await LoginWithFlow(cancellationToken);

                if (result.IsFail)
                {
                    await UniTask.Delay(RetryInterval, cancellationToken: cancellationToken);
                }
            } while (result.IsFail);
        }

        private async UniTask<Result> LoginWithFlow(CancellationToken cancellationToken)
        {
#if UNITY_ANDROID && !UNITY_EDITOR_OSX
            var userName = GetUserName();
            var password = GetPassword();

            var result = await unityGameServices.SignInWithUsernamePassword(userName, password, cancellationToken);
            if (result.IsOk)
            {
                return result;
            }

            return await unityGameServices.SignUpWithUsernamePassword(userName, password, cancellationToken);
#else
            return await unityGameServices.SignInAnonymously(cancellationToken);
#endif
        }

        private async UniTask SyncRemoteConfig(CancellationToken cancellationToken)
        {
            await remoteConfigService.SyncRemoteConfig(cancellationToken);
        }

        [UsedImplicitly]
        private async UniTask SyncEconomyConfiguration(CancellationToken cancellationToken)
        {
            Result result;

            do
            {
                result = await unityGameServices.SyncEconomyConfiguration(cancellationToken);

                if (result.IsFail)
                {
                    await UniTask.Delay(RetryInterval, cancellationToken: cancellationToken);
                }
            } while (result.IsFail);
        }

        private async UniTask SyncUserNameIfNotValid(CancellationToken cancellationToken)
        {
            var result = await unityGameServices.GetUserName(cancellationToken);

            if (result.IsFail)
            {
                return;
            }

            var userName = result.Value;
            if (userName != oculusClient.UserName)
            {
                await unityGameServices.SetUserName(oculusClient.UserName, cancellationToken);
            }
        }

        private async UniTask RefreshFirebaseToken(CancellationToken cancellationToken)
        {
            Result result;

            do
            {
                result = await firebaseTokenService.RefreshToken(cancellationToken);

                if (result.IsFail)
                {
                    await UniTask.Delay(RetryInterval, cancellationToken: cancellationToken);
                }
            } while (result.IsFail);
        }

        private void InitializeAnalytics()
        {
            analyticsService.Initialize();
            analyticsService.SetupUser(unityGameServices.UserId, oculusClient.UserName);
        }

        [UsedImplicitly]
        private string GetUserName()
        {
            return $"{oculusClient.OrgScopedUserId}";
        }

        [UsedImplicitly]
        private string GetPassword()
        {
            return $"{oculusClient.OrgScopedUserId}_Pw!";
        }
    }
}