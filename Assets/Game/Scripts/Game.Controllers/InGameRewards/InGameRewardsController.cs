using Cysharp.Threading.Tasks;
using Game.Views.GadgetShop;
using Game.Views.InGameRewards;
using Game.Views.Interactables;
using Game.Views.Levels;
using MessagePipe;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Controllers.InGameRewards
{
    public class InGameRewardsController : ControllerBase
    {
        private LevelModel levelModel;
        private InteractablesManager interactablesManager;
        private GadgetShopConfig gadgetShopConfig;

        [Inject]
        private void Construct(LevelModel levelModel, ISubscriber<RewardArgs> rewardSubscriber, InteractablesManager interactablesManager, GadgetShopConfig gadgetShopConfig)
        {
            this.levelModel = levelModel;
            this.interactablesManager = interactablesManager;
            this.gadgetShopConfig = gadgetShopConfig;

            rewardSubscriber.Subscribe(HandleReward).AddTo(DisposeCancellationToken);
        }

        private void HandleReward(RewardArgs args)
        {
            if (levelModel.IsLevelLoading.Value)
            {
                return;
            }

            if (args.code != 3 && args.code != 4)
            {
                return;
            }

            var rewardProbability = Random.value;

            var possibleRewards = gadgetShopConfig.GadgetShopDataList.FindAll(x => x.rewardProbability > rewardProbability);
            if (possibleRewards.Count == 0)
            {
                return;
            }

            var randomGadget = possibleRewards.RandomItem();
            interactablesManager.CreateActor(randomGadget.viewCode, new Pose(args.position, Quaternion.identity));
        }
    }
}