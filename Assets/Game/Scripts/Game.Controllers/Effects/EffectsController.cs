using System;
using System.Reactive.Linq;
using Cysharp.Threading.Tasks;
using Fusion;
using Game.Models;
using Game.Views.Checkpoints;
using Game.Views.Effects;
using Game.Views.Network;
using Game.Views.Players;
using Game.Views.PlayerUI;
using Game.Views.UI.Screens.Challenges;
using MessagePipe;
using Modules.Core;
using Modules.UI;
using UnityEngine;
using VContainer;
using Monsters_MonsterDamageArgs = Game.Views.Monsters.MonsterDamageArgs;

namespace Game.Controllers.Effects
{
    public class EffectsController : ControllerBase
    {
        private NetworkWire networkWire;
        private PlayersModel playersModel;
        private EffectsConfig effectConfig;
        private EffectsManager effectsManager;
        private ChallengesScreen challengesScreen;

        private PlayerActor LocalPlayer => playersModel.LocalPlayer.Value;

        [Inject]
        private void Construct(
            PlayersModel playersModel,
            EconomyModel economyModel,
            EffectsManager effectsManager,
            IScreenManager screenManager,
            NetworkWire networkWire,
            CheckpointsManager checkpointsManager,
            PlayerMenu playerMenu,
            ISubscriber<Monsters_MonsterDamageArgs> damageSubscriber)
        {
            this.networkWire = networkWire;
            this.playersModel = playersModel;
            this.effectsManager = effectsManager;
            challengesScreen = screenManager.GetScreen<ChallengesScreen>();

            networkWire.OnEffectSpawnReceived.Subscribe(HandleEffectSpawn).AddTo(DisposeCancellationToken);
            economyModel.OnPurchaseCompleted.Subscribe(_ => RenderPurchaseEffect()).AddTo(DisposeCancellationToken);
            economyModel.OnDiamondsReceived.Subscribe(_ => RenderPurchaseDiamondEffect()).AddTo(DisposeCancellationToken);
            challengesScreen.OnClaimChallengeButtonClicked.Subscribe(_ => RenderPurchaseEffect()).AddTo(DisposeCancellationToken);
            checkpointsManager.OnRaceCompleted.Where(raceData => !raceData.isCancel).Subscribe(_ => RenderRaceEndEffect()).AddTo(DisposeCancellationToken);
            playerMenu.MoneyBagCollectMenu.OnCollected.Subscribe(_ => RenderPurchaseEffect()).AddTo(DisposeCancellationToken);
            damageSubscriber.Subscribe(HandleDamage).AddTo(DisposeCancellationToken);
        }

        private void RenderPurchaseEffect()
        {
            InstantiateEffect((byte)EffectId.Purchase);
        }

        private void RenderPurchaseDiamondEffect()
        {
            InstantiateEffect((byte)EffectId.PurchaseDiamond);
        }

        private void RenderRaceEndEffect()
        {
            InstantiateEffect((byte)EffectId.RaceEnd);
        }

        private void InstantiateEffect(byte code)
        {
            if (LocalPlayer == null)
            {
                return;
            }

            var playerList = playersModel.FindPlayers(LocalPlayer.transform.position, 5);
            foreach (var player in playerList)
            {
                networkWire.SendEffectSpawn(player.Object.StateAuthority, code, playersModel.LocalPlayer.Value.transform.position);
            }
        }

        private void HandleEffectSpawn((PlayerRef playerRef, byte code, Vector3 position) args)
        {
            if (!playersModel.TryGetPlayer(args.playerRef, out _) || LocalPlayer == null)
            {
                return;
            }

            effectsManager.CreateEffect((EffectId)args.code, new Pose(args.position, Quaternion.identity), 4);
        }

        private void HandleDamage(Monsters_MonsterDamageArgs args)
        {
            effectsManager.CreateEffect(EffectId.SpiderHit, new Pose(args.monster.gameObject.transform.position, Quaternion.identity), 4);
        }
    }
}