using System;
using Cysharp.Threading.Tasks;
using Game.Views.Players;
using Modules.Core;
using Modules.XR;
using VContainer;

namespace Game.Controllers.Players
{
    public class PlayerTeleportController : ControllerBase
    {
        private bool isTeleporting;
        private IXRPlayer xrPlayer;
        private PlayersModel playersModel;

        [Inject]
        private void Construct(PlayersModel playersModel, IXRPlayer xrPlayer)
        {
            this.xrPlayer = xrPlayer;
            this.playersModel = playersModel;

            playersModel.OnLocalPlayerTeleporting.Subscribe(args => TeleportPlayer(args).Forget()).AddTo(DisposeCancellationToken);
        }

        private async UniTaskVoid TeleportPlayer(PlayerTeleportArgs args)
        {
            if (isTeleporting)
            {
                return;
            }

            isTeleporting = true;

            playersModel.SetActiveLocomotions(false);
            await UniTask.Yield(PlayerLoopTiming.FixedUpdate, DisposeCancellationToken);

            xrPlayer.SetTransformRotation(args.pose.rotation.eulerAngles.y);
            xrPlayer.SetTransformPosition(args.pose.position);

            if (args.enableLocomotions)
            {
                playersModel.SetActiveLocomotions(true);
            }

            isTeleporting = false;
        }
    }
}