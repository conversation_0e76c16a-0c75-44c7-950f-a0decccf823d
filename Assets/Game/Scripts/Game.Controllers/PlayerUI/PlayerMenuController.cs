using System;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Views.Players;
using Game.Views.PlayerUI;
using Modules.Core;
using Modules.XR;
using VContainer;

namespace Game.Controllers.PlayerUI
{
    public class PlayerMenuController : ControllerBase
    {
        private IXRPlayer xrPlayer;
        private PlayerMenu playerMenu;

        [Inject]
        private void Construct(PlayerMenu playerMenu, PlayersModel playersModel, IXRPlayer xrPlayer)
        {
            this.xrPlayer = xrPlayer;
            this.playerMenu = playerMenu;

            playersModel.LocalPlayer.Subscribe(HandleLocalPlayer).AddTo(DisposeCancellationToken);
        }

        private void HandleLocalPlayer(PlayerActor player)
        {
            if (player == null)
            {
                playerMenu.SetActive(false);
            }
            else
            {
                playerMenu.SetActive(true);
                xrPlayer.OnPoseUpdated.Subscribe(_ => playerMenu.SetPose(player)).AddTo(player);
                player.Scale.Subscribe(playerMenu.SetScale).AddTo(player);
            }
        }
    }
}