using Cysharp.Threading.Tasks;
using Game.Views.Consumeables;
using Game.Views.Players;
using MessagePipe;
using Modules.Core;
using VContainer;

namespace Game.Controllers.Consumables
{
    public class ConsumeablesConsumeController : ControllerBase
    {
        [Inject]
        private void Construct(ISubscriber<ConsumableConsumeArgs> consumeSubscriber)
        {
            consumeSubscriber.Subscribe(HandleConsume).AddTo(DisposeCancellationToken);
        }

        private void HandleConsume(ConsumableConsumeArgs args)
        {
            if (!args.collider.TryGetComponent(out PlayerActor player))
            {
                return;
            }

            switch (args.actor)
            {
                case HealthActor healthActor:
                    HandleAppleHealth(healthActor, player);
                    break;
                case AmmoActor ammoActor:
                    HandleGrenadeLauncherAmmo(ammoActor, player);
                    break;
            }
        }

        private void HandleAppleHealth(HealthActor healthActor, PlayerActor playerActor)
        {
            playerActor.ConsumeMedicalRpc((byte)healthActor.Health);
            healthActor.DestroyActor();
        }

        private void HandleGrenadeLauncherAmmo(AmmoActor ammoActor, PlayerActor playerActor)
        {
            ammoActor.DestroyActor();
        }
    }
}