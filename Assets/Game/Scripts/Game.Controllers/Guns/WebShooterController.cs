using System;
using Cysharp.Threading.Tasks;
using Game.View.Locomotions;
using Game.Views.Guns;
using Game.Views.InteractablesCore;
using Modules.Core;
using Modules.Network;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;
using LocomotionSystem = Game.View.Locomotions.LocomotionSystem;

namespace Game.Controllers.Guns
{
    public class WebShooterController : ControllerBase
    {
        private LocomotionSystem locomotionSystem;

        [Inject]
        private void Construct(IInteractableInteractionSubscriber interactableInteractionsSubscriber, LocomotionSystem locomotionSystem, INetworkClient networkClient)
        {
            this.locomotionSystem = locomotionSystem;

            interactableInteractionsSubscriber.OnSelectEntered.Subscribe(HandleSelectEntered).AddTo(DisposeCancellationToken);
            networkClient.OnShutdown.Subscribe(_ => HandleShutdown()).AddTo(DisposeCancellationToken);
        }

        private void HandleSelectEntered(InteractionArgs<SelectEnterEventArgs> args)
        {
            if (args.interactable is not WebShooterActor webShooter)
            {
                return;
            }

            webShooter.OnHooked.Subscribe(HandleHook).AddTo(webShooter.DropCancellationToken);
            webShooter.OnUnhooked.Subscribe(HandleUnhook).AddTo(webShooter.DropCancellationToken);
        }

        private void HandleHook(WebHookArgs args)
        {
            if (!locomotionSystem.TryGetLocomotion<WebLocomotion>(out var webLocomotion))
            {
                return;
            }

            if (!webLocomotion.IsHooked.Value)
            {
                webLocomotion.Run();
            }

            webLocomotion.Hook(args);
        }

        private void HandleUnhook(WebUnhookArgs args)
        {
            if (!locomotionSystem.TryGetLocomotion<WebLocomotion>(out var webLocomotion))
            {
                return;
            }

            webLocomotion.Unhook(args);

            if (!webLocomotion.IsHooked.Value)
            {
                webLocomotion.Stop();
            }
        }

        private void HandleShutdown()
        {
            if (!locomotionSystem.TryGetLocomotion<WebLocomotion>(out var webLocomotion))
            {
                return;
            }

            webLocomotion.Stop();
        }
    }
}