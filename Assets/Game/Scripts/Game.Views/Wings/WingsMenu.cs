using System;
using System.Reactive.Subjects;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core;
using Game.Views.Players;
using Modules.Core;
using Modules.UI;
using Modules.XR;
using UnityEngine;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;

namespace Game.Views.Wings
{
    public class WingsMenu : Actor
    {
        [SerializeField] private Transform node;
        [SerializeField] private WingsSlot wingsSlot;
        [SerializeField] private ActivationTrigger activationTrigger;
        [SerializeField] private DeactivationTrigger deactivationTrigger;

        private readonly ISubject<WingsActor> onWingsCreating = new Subject<WingsActor>();
        private readonly ISubject<IXRSelectInteractor> onWingsDestroying = new Subject<IXRSelectInteractor>();

        private IXRInput xrInput;
        private IXRPlayer xrPlayer;
        private IAudioClient audioClient;
        private PlayersModel playersModel;

        public IObservable<WingsActor> OnWingsCreating => onWingsCreating;
        public IObservable<IXRSelectInteractor> OnWingsDestroying => onWingsDestroying;

        private PlayerActor LocalPlayer => playersModel.LocalPlayer.Value;
        private bool HasAttachedWings => LocalPlayer != null && LocalPlayer.WingId != 0;

        [Inject]
        private void Construct(PlayersModel playersModel, IXRPlayer xrPlayer, IXRInput xrInput, IAudioClient audioClient, WingsManager wingsManager)
        {
            this.xrInput = xrInput;
            this.xrPlayer = xrPlayer;
            this.playersModel = playersModel;
            this.audioClient = audioClient;
            wingsSlot.Initialize(wingsManager);

            playersModel.LocalPlayer.Subscribe(HandleLocalPlayer).AddTo(destroyCancellationToken);
        }

        private void HandleLocalPlayer(PlayerActor player)
        {
            if (player == null)
            {
                UngrabAsync().Forget();
            }
            else
            {
                xrPlayer.OnPoseUpdated.Subscribe(_ => UpdatePose()).AddTo(player);
                xrInput.OnGrab.Subscribe(HandleGrab).AddTo(player);
                xrInput.OnUngrab.Subscribe(HandleUngrab).AddTo(player);
                activationTrigger.OnHoverStarted.Subscribe(HandleActivationTriggerHoverStarted).AddTo(player);
                activationTrigger.OnHoverEnded.Subscribe(HandleActivationTriggerHoverEnded).AddTo(player);
                activationTrigger.OnTriggered.Subscribe(HandleActivationTriggerTriggered).AddTo(player);
                deactivationTrigger.OnHoverStarted.Subscribe(HandleDeactivationHoverStarted).AddTo(player);
                deactivationTrigger.OnHoverEnded.Subscribe(HandleDeactivationHoverEnded).AddTo(player);
                deactivationTrigger.OnTriggered.Subscribe(HandleDeactivationTriggerTriggered).AddTo(player);
                player.Scale.Subscribe(SetScale).AddTo(player);
            }
        }

        private void SetScale(float scale)
        {
            node.localScale = Vector3.one * scale;
        }

        private void HandleGrab(SelectEnterEventArgs args)
        {
            if (!TryGetWingsActor(args.interactableObject, out var wings) || HasAttachedWings)
            {
                return;
            }

            wingsSlot.Show(wings.InteractableId);
            activationTrigger.SetActive(true);
            deactivationTrigger.SetActive(false);
        }

        private void HandleUngrab(SelectExitEventArgs args)
        {
            if (!TryGetWingsActor(args.interactableObject, out _) || HasAttachedWings)
            {
                return;
            }

            UngrabAsync().Forget();
        }

        private async UniTaskVoid UngrabAsync()
        {
            await UniTask.Yield(destroyCancellationToken);

            if (HasAttachedWings)
            {
                wingsSlot.SetHoverEndState();
                wingsSlot.SetDefaultView();
            }
            else
            {
                wingsSlot.Hide();
            }

            activationTrigger.SetActive(false);
        }

        private void HandleActivationTriggerHoverStarted(IXRInteractor interactor)
        {
            wingsSlot.SetHoverStartState();
            xrInput.SendHapticImpulse(interactor, HapticImpulse.ShortDurationLowAmplitude);
        }

        private void HandleActivationTriggerHoverEnded(IXRInteractor interactor)
        {
            wingsSlot.SetHoverEndState();
        }

        private void HandleActivationTriggerTriggered(WingsActor actor)
        {
            audioClient.Play(AudioKeys.AttachWing, wingsSlot.transform.position, destroyCancellationToken);
            onWingsCreating.OnNext(actor);
            deactivationTrigger.SetActive(true);
            wingsSlot.SetHoverEndState();
        }

        private void HandleDeactivationHoverStarted(IXRInteractor interactor)
        {
            wingsSlot.SetHoverStartState();
            xrInput.SendHapticImpulse(interactor, HapticImpulse.ShortDurationLowAmplitude);
        }

        private void HandleDeactivationHoverEnded(IXRInteractor interactor)
        {
            wingsSlot.SetHoverEndState();
        }

        private void HandleDeactivationTriggerTriggered(IXRSelectInteractor interactor)
        {
            audioClient.Play(UIAudioKeys.ButtonClick, wingsSlot.transform.position, destroyCancellationToken);
            onWingsDestroying.OnNext(interactor);
            deactivationTrigger.SetActive(false);
            wingsSlot.SetHoverEndState();
        }

        private void UpdatePose()
        {
            if (!LocalPlayer.TryGetAvatarHandPose(out var leftHandPose, out var rightHandPose))
            {
                return;
            }

            var scale = node.localScale.x;
            var direction = (leftHandPose.shoulderPosition - rightHandPose.shoulderPosition).OnlyXZ();
            var rotation = Quaternion.LookRotation(direction) * Quaternion.AngleAxis(90, Vector3.up);
            var forward = rotation * Vector3.forward;
            var position = xrPlayer.BodyPosition + 0.075f * scale * Vector3.up + 0.125f * scale * forward;
            node.SetPositionAndRotation(position, rotation);
        }

        private static bool TryGetWingsActor(IXRInteractable interactable, out WingsActor actor)
        {
            return interactable.transform.TryGetComponent(out actor);
        }
    }
}