using System.Collections.Generic;
using Game.Core;
using Game.Core.Data;

namespace Game.Views.Interactables
{
    public class InteractablesSorter
    {
        private readonly InteractablesConfig interactablesConfig;

        public InteractablesSorter(InteractablesConfig interactablesConfig)
        {
            this.interactablesConfig = interactablesConfig;
        }

        public List<string> GetSortedList(List<ShopInventoryData> inventoryList)
        {
            var list = inventoryList.ConvertAll(i => i.viewCode);
            list.Sort(CompareInteractables);
            return list;
        }

        private int CompareInteractables(string code1, string code2)
        {
            var value1 = GetInteractableValue(code1);
            var value2 = GetInteractableValue(code2);

            if (value1 > value2)
            {
                return 1;
            }

            if (value1 < value2)
            {
                return -1;
            }

            return 0;
        }

        private int GetInteractableValue(string code)
        {
            if (InventoryCodes.BlockTool == code)
            {
                return 500;
            }

            if (interactablesConfig.IsWeapon(code))
            {
                return 600;
            }

            if (interactablesConfig.IsGun(code))
            {
                return 700;
            }

            if (interactablesConfig.IsWing(code))
            {
                return 800;
            }

            if (interactablesConfig.IsItem(code))
            {
                return 900;
            }

            return 0;
        }
    }
}