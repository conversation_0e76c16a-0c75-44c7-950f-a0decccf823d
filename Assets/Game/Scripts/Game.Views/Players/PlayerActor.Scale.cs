using Cysharp.Threading.Tasks;
using Modules.Core;
using UnityEngine;

namespace Game.Views.Players
{
    public partial class PlayerActor
    {
        private readonly IAsyncReactiveProperty<float> scale = new AsyncReactiveProperty<float>(CoreConstants.WorldScale);
        public IReadOnlyAsyncReactiveProperty<float> Scale => scale;

        public override void SetScale(float newScale)
        {
            if (HasStateAuthority)
            {
                networkTransform.transform.localScale = newScale * Vector3.one;
                scale.Value = newScale;
            }
        }
    }
}
