using System;
using System.Reactive.Subjects;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core;
using Game.Views.InteractablesCore;
using Game.Views.Items;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.Ores
{
    public class OreConverterView : Actor
    {
        private const float ResolveDelay = 0.4f;

        [SerializeField] private Collider selfCollider;
        [SerializeField] private GameObject vfxObject;

        private readonly ISubject<InteractableActor> onInteractableEntered = new Subject<InteractableActor>();
        private readonly ISubject<BackpackActor> onBackpackEntered = new Subject<BackpackActor>();

        private bool ignoreTrigger;
        private IAudioClient audioClient;

        public IObservable<InteractableActor> OnInteractableEntered => onInteractableEntered;
        public IObservable<BackpackActor> OnBackpackEntered => onBackpackEntered;

        [Inject]
        private void Construct(IAudioClient audioClient)
        {
            this.audioClient = audioClient;
        }

        private void OnDisable()
        {
            ignoreTrigger = false;
        }

        private void OnTriggerStay(Collider other)
        {
            if (ignoreTrigger)
            {
                return;
            }

            if (other.attachedRigidbody.TryGetComponent(out BackpackActor backpack))
            {
                ProcessBackpack(backpack);
            }
            else if (other.attachedRigidbody.TryGetComponent(out InteractableActor interactable))
            {
                ProcessInteractable(interactable);
            }
        }

        private bool IsValid(InteractableActor interactable)
        {
            return interactable != null && interactable.HasStateAuthority && !interactable.IsGrabbed;
        }

        private bool IsValid(BackpackActor backpack)
        {
            return backpack != null && backpack.HasStateAuthority && !backpack.IsGrabbed && !backpack.IsEquipped;
        }

        private void ProcessBackpack(BackpackActor backpack)
        {
            if (!IsValid(backpack))
            {
                return;
            }

            ResolveEnter(() =>
            {
                if (IsValid(backpack))
                {
                    onBackpackEntered.OnNext(backpack);
                    RenderVfx();
                }
            }).Forget();
        }

        private void ProcessInteractable(InteractableActor interactable)
        {
            if (!IsValid(interactable))
            {
                return;
            }

            ResolveEnter(() =>
            {
                if (IsValid(interactable))
                {
                    onInteractableEntered.OnNext(interactable);
                    RenderVfx();
                }
            }).Forget();
        }

        private async UniTaskVoid ResolveEnter(Action callback)
        {
            ignoreTrigger = true;
            selfCollider.enabled = false;
            await UniTask.Delay(TimeSpan.FromSeconds(ResolveDelay), cancellationToken: destroyCancellationToken);
            callback?.Invoke();
            await UniTask.Yield(destroyCancellationToken);
            selfCollider.enabled = true;
            ignoreTrigger = false;
        }

        private void RenderVfx()
        {
            vfxObject.SetActive(true);
            audioClient.Play(AudioKeys.ConverterMachine, transform.position, destroyCancellationToken);
            UniTaskAsyncEnumerable
                .Timer(TimeSpan.FromSeconds(3))
                .Subscribe(_ => vfxObject.SetActive(false))
                .AddTo(destroyCancellationToken);
        }
    }
}