using System;
using System.Reactive.Subjects;
using System.Threading;
using Cysharp.Threading.Tasks;
using Modules.Core;
using Modules.XR;
using UnityEngine;
using UnityEngine.InputSystem;

namespace Game.View.Locomotions
{
    public class ClimbLocomotion : LocomotionBase
    {
        [SerializeField] private float handRadius = 0.15f;
        [SerializeField] private float maxJumpSpeed = 3.5f;
        [SerializeField] private float releaseDistanceThreshold = 0.15f;

        [Space] [SerializeField] private ClimbLocomotionHand leftHand;
        [SerializeField] private ClimbLocomotionHand rightHand;

        [Space]
        [Tooltip("Interaction with these layers will be prioritized over climbing")]
        [SerializeField]
        private LayerMask priorityLayers;

        private ClimbLocomotionHand climbingHand;
        private CancellationTokenSource inputCancellationTokenSource;
        private bool isClimbing;
        private Vector3 climbAnchorPosition;
        private Vector3 lastRightHandLocalPosition;
        private Vector3 lastLeftHandLocalPosition;

        private readonly ISubject<HandAction> onHandActionLeft = new Subject<HandAction>();
        private readonly ISubject<HandAction> onHandActionRight = new Subject<HandAction>();
        private readonly ISubject<Vector3> onClimbStarted = new Subject<Vector3>();
        private readonly ISubject<Vector3> onClimbEnded = new Subject<Vector3>();

        public IObservable<HandAction> OnHandActionLeft => onHandActionLeft;
        public IObservable<HandAction> OnHandActionRight => onHandActionRight;
        public IObservable<Vector3> OnClimbStarted => onClimbStarted;
        public IObservable<Vector3> OnClimbEnded => onClimbEnded;

        public enum HandActionType
        {
            Grab,
            Release,
            ForceRelease
        }

        public struct HandAction
        {
            public HandActionType type;
            public Vector3 position;
        }

        public override void Construct(IXRPlayer xrPlayer, IXRInput xrInput, IAudioClient audioClient)
        {
            base.Construct(xrPlayer, xrInput, audioClient);
            leftHand.OnHandAction.Subscribe(handAction => onHandActionLeft.OnNext(handAction)).AddTo(destroyCancellationToken);
            rightHand.OnHandAction.Subscribe(handAction => onHandActionRight.OnNext(handAction)).AddTo(destroyCancellationToken);

            InitializeValues();
        }

        public override void Run()
        {
            base.Run();

            EnableXRFilters(true);

            inputCancellationTokenSource.CancelAndDispose();
            inputCancellationTokenSource = new CancellationTokenSource();

            XRInput.OnRightSelect.Subscribe(context => HandleClimbHandInput(context, rightHand, leftHand)).AddTo(inputCancellationTokenSource.Token);
            XRInput.OnLeftSelect.Subscribe(context => HandleClimbHandInput(context, leftHand, rightHand)).AddTo(inputCancellationTokenSource.Token);
        }

        public override void Stop()
        {
            base.Stop();
            EnableXRFilters(false);

            if (isClimbing)
                ReleaseClimbAndJump();

            isClimbing = false;

            inputCancellationTokenSource.CancelAndDispose();
        }

        private void SetRigidbodyForClimb(bool enable)
        {
            Rigidbody.useGravity = !enable;
            Rigidbody.interpolation = enable ? RigidbodyInterpolation.None : RigidbodyInterpolation.Interpolate;
        }

        private void InitializeValues()
        {
            leftHand.Position = rightHand.Position = HeadNode.position;

            // hands need to calculate their speed relative to XR rig because they won't be changing their world position while climbing
            leftHand.Initialize(RigNode, HeadNode, LeftHandNode, XRInput.LeftDirectInteractor, handRadius, layers, priorityLayers);
            rightHand.Initialize(RigNode, HeadNode, RightHandNode, XRInput.RightDirectInteractor, handRadius, layers, priorityLayers);
        }

        /// <summary>
        /// Handles an attempt to climb with a new active hand.
        /// If new hand has a valid climbing target, we set it as the active hand and force the other hand to release
        /// and transfer control.
        /// </summary>
        private void HandleClimbHandInput(InputAction.CallbackContext obj, ClimbLocomotionHand activeHand, ClimbLocomotionHand otherHand)
        {
            if (obj.started && activeHand.CanClimb)
            {
                StartClimb(activeHand, otherHand);
            }
            else if (obj.canceled && activeHand.IsClimbing)
            {
                ReleaseClimbAndJump();
            }
        }

        private void Update()
        {
            if (!IsRun || !IsActive.Value) return;

            if (!isClimbing)
            {
                MirrorRigHandPositions();
                StoreLastPositions();
                return;
            }

            if (IsHandTooFarAwayFromAnchor())
            {
                ReleaseClimbAndJump(true);
                return;
            }

            var rootMovement = Vector3.zero;

            // calculate hand movement in XR rig local space
            if (rightHand.IsClimbing)
                AddHandMovement(RightHandNode, lastRightHandLocalPosition, ref rootMovement);
            else if (leftHand.IsClimbing)
                AddHandMovement(LeftHandNode, lastLeftHandLocalPosition, ref rootMovement);

            MirrorRigHandPositions();
            // it's critical to cache last positions BEFORE assigning the new RigNode position
            StoreLastPositions();

            if (rootMovement != Vector3.zero)
            {
                RigNode.position += rootMovement;
                Rigidbody.velocity = Vector3.zero;
            }
        }

        private void AddHandMovement(Transform handNode, Vector3 lastHandLocalPosition, ref Vector3 rootMovement)
        {
            var deltaPositionLocal = handNode.localPosition - lastHandLocalPosition;
            var deltaPositionWorld = RigNode.TransformVector(deltaPositionLocal);
            rootMovement += -deltaPositionWorld;
        }

        private bool IsHandTooFarAwayFromAnchor()
        {
            return (climbingHand.Position - climbAnchorPosition).magnitude > releaseDistanceThreshold;
        }

        private void StoreLastPositions()
        {
            lastRightHandLocalPosition = RightHandNode.localPosition;
            lastLeftHandLocalPosition = LeftHandNode.localPosition;
        }

        private void MirrorRigHandPositions()
        {
            leftHand.SetPose(LeftHandNode.position, LeftHandNode.rotation);
            rightHand.SetPose(RightHandNode.position, RightHandNode.rotation);
        }

        private void StartClimb(ClimbLocomotionHand newHand, ClimbLocomotionHand otherHand)
        {
            // set newHand as the climbing hand
            climbingHand = newHand;
            climbingHand.GrabClimbTarget();

            SetRigidbodyForClimb(true);

            if (!isClimbing) onClimbStarted.OnNext(climbingHand.Position);

            isClimbing = true;
            climbAnchorPosition = climbingHand.Position;

            // make other hand release, we only want one hand to control the climbing movement    
            otherHand.Release(HandActionType.Release);
        }

        private void ReleaseClimbAndJump(bool forced = false)
        {
            var handLocalVelocity = climbingHand.LocalVelocityAverage;
            var jumpVelocity = -RigNode.TransformVector(handLocalVelocity); // note that we negate to move in the opposite direction of the hand!
            var grabPosition = climbingHand.Position;

            climbingHand.Release(forced ? HandActionType.ForceRelease : HandActionType.Release);
            climbingHand = null;

            SetRigidbodyForClimb(false);

            isClimbing = false;

            // apply clamped velocity
            jumpVelocity = Vector3.ClampMagnitude(jumpVelocity, maxJumpSpeed);
            Rigidbody.velocity = jumpVelocity;

            onClimbEnded.OnNext(grabPosition);
        }

        private void EnableXRFilters(bool enable)
        {
            leftHand.EnableXRFilters(enable);
            rightHand.EnableXRFilters(enable);
        }
    }
}