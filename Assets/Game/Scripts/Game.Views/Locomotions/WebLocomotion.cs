using Cysharp.Threading.Tasks;
using Modules.Core;
using Modules.XR;
using UnityEngine;

namespace Game.View.Locomotions
{
    public class WebLocomotion : LocomotionBase
    {
        [SerializeField] private WebLocomotionConfig config;

        private WebHand leftWebHand;
        private WebHand rightWebHand;
        private readonly IAsyncReactiveProperty<bool> isHooked = new AsyncReactiveProperty<bool>(false);

        public IReadOnlyAsyncReactiveProperty<bool> IsHooked => isHooked;

        public override void Construct(IXRPlayer xrPlayer, IXRInput xrInput, IAudioClient audioClient)
        {
            base.Construct(xrPlayer, xrInput, audioClient);
            leftWebHand = new WebHand(config, RigNode, LeftHandNode);
            rightWebHand = new WebHand(config, RigNode, RightHandNode);
        }

        public override void Stop()
        {
            base.Stop();
            leftWebHand.Unhook();
            rightWebHand.Unhook();
            UpdateHookState();
        }

        private void FixedUpdate()
        {
            if (!IsRun || !IsActive.Value)
            {
                return;
            }

            UpdateLocomotion();
            UpdateHookState();
        }

        public void Hook(WebHookArgs args)
        {
            if (!IsActive.Value)
            {
                return;
            }

            GetWebHand(args.handType).Hook(args.anchor);
        }

        public void Unhook(WebUnhookArgs args)
        {
            GetWebHand(args.handType).Unhook();
        }

        private WebHand GetWebHand(HandType handType)
        {
            return handType == HandType.Left ? leftWebHand : rightWebHand;
        }

        private void UpdateLocomotion()
        {
            leftWebHand.Update();
            rightWebHand.Update();

            if (leftWebHand.Force.IsNotZero() || rightWebHand.Force.IsNotZero())
            {
                Rigidbody.AddForce(leftWebHand.Force + rightWebHand.Force, ForceMode.Acceleration);
            }
        }

        private void UpdateHookState()
        {
            var isHookedValue = leftWebHand.IsHooked || rightWebHand.IsHooked;

            if (isHooked.Value == isHookedValue)
            {
                return;
            }

            isHooked.Value = isHookedValue;
        }
    }
}